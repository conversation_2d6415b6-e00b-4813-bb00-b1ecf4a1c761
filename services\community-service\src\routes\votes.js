const express = require('express');
const router = express.Router();
const { db, collections } = require('../config/firebase');
const Logger = require('../../shared/utils/logger');

const logger = new Logger('community-service');

// Helper function to update post vote count
async function updatePostVoteCount(linkId) {
  try {
    const votesSnapshot = await db.collection(collections.VOTES)
      .where('linkId', '==', linkId)
      .get();

    const voteCount = votesSnapshot.size;

    // Try to update in both 'posts' and 'links' collections
    const postsQuery = await db.collection(collections.POSTS)
      .where('id', '==', linkId)
      .get();

    if (!postsQuery.empty) {
      await postsQuery.docs[0].ref.update({ voteCount });
    }

    // Also try links collection (for backward compatibility)
    try {
      const linkRef = db.collection('links').doc(linkId);
      const linkDoc = await linkRef.get();
      if (linkDoc.exists) {
        await linkRef.update({ voteCount });
      }
    } catch (error) {
      // Ignore if links collection doesn't exist
      logger.debug('Links collection update failed (expected if not using links collection)', { linkId });
    }

    logger.info('Vote count updated', { linkId, voteCount });
    return voteCount;
  } catch (error) {
    logger.error('Update vote count error', { error: error.message, linkId });
    throw error;
  }
}

// Submit or update vote for a link
router.post('/:linkId', async (req, res) => {
  try {
    const { linkId } = req.params;
    const { voteType, userId, userEmail } = req.body; // 'safe', 'unsafe', 'suspicious'

    logger.info('Vote submission request', { linkId, voteType, userId });

    // Validate required fields
    if (!linkId || !voteType || !userId) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: linkId, voteType, userId'
      });
    }

    // Check if user already voted
    const existingVoteQuery = await db.collection(collections.VOTES)
      .where('linkId', '==', linkId)
      .where('userId', '==', userId)
      .get();

    let voteDoc;
    if (!existingVoteQuery.empty) {
      // Update existing vote
      voteDoc = existingVoteQuery.docs[0];
      await voteDoc.ref.update({
        voteType,
        updatedAt: new Date()
      });
      logger.info('Vote updated', { linkId, userId, voteType });
    } else {
      // Create new vote
      const newVote = {
        linkId,
        userId,
        userEmail: userEmail || null,
        voteType,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      voteDoc = await db.collection(collections.VOTES).add(newVote);
      logger.info('New vote created', { linkId, userId, voteType });
    }

    // Update vote count on the post
    await updatePostVoteCount(linkId);

    res.json({
      success: true,
      message: 'Vote recorded successfully',
      vote: {
        id: voteDoc.id,
        linkId,
        userId,
        voteType,
        createdAt: new Date().toISOString()
      }
    });
  } catch (error) {
    logger.error('Vote submission error', { error: error.message, linkId: req.params.linkId });
    res.status(500).json({
      success: false,
      error: 'Failed to submit vote',
      message: error.message
    });
  }
});

// Get vote statistics for a link
router.get('/:linkId/stats', async (req, res) => {
  try {
    const { linkId } = req.params;

    const votesSnapshot = await db.collection(collections.VOTES)
      .where('linkId', '==', linkId)
      .get();

    const stats = {
      total: 0,
      safe: 0,
      unsafe: 0,
      suspicious: 0
    };

    votesSnapshot.forEach((doc) => {
      const voteType = doc.data().voteType;
      stats.total++;
      if (stats[voteType] !== undefined) {
        stats[voteType]++;
      }
    });

    res.json({
      success: true,
      data: {
        linkId,
        ...stats
      }
    });
  } catch (error) {
    logger.error('Vote stats error', { error: error.message, linkId: req.params.linkId });
    res.status(500).json({
      success: false,
      error: 'Failed to get vote stats'
    });
  }
});

// Get user's vote for a specific link
router.get('/:linkId/user', async (req, res) => {
  try {
    const { linkId } = req.params;
    const userId = req.query.userId || req.headers['x-user-id'];

    if (!userId) {
      return res.status(400).json({
        success: false,
        error: 'User ID required'
      });
    }

    const userVoteQuery = await db.collection(collections.VOTES)
      .where('linkId', '==', linkId)
      .where('userId', '==', userId)
      .get();

    if (!userVoteQuery.empty) {
      const voteDoc = userVoteQuery.docs[0];
      const voteData = voteDoc.data();

      res.json({
        success: true,
        data: {
          id: voteDoc.id,
          linkId,
          userId: voteData.userId,
          voteType: voteData.voteType,
          createdAt: voteData.createdAt.toISOString()
        }
      });
    } else {
      res.json({
        success: true,
        data: null
      });
    }
  } catch (error) {
    logger.error('Get user vote error', { error: error.message, linkId: req.params.linkId });
    res.status(500).json({
      success: false,
      error: 'Failed to get user vote'
    });
  }
});

// Get optimized vote data (stats + user vote)
router.get('/:linkId/optimized', async (req, res) => {
  try {
    const { linkId } = req.params;
    const userId = req.query.userId || req.headers['x-user-id'];

    // Get vote statistics
    const votesSnapshot = await db.collection(collections.VOTES)
      .where('linkId', '==', linkId)
      .get();

    const stats = {
      total: 0,
      safe: 0,
      unsafe: 0,
      suspicious: 0
    };

    votesSnapshot.forEach((doc) => {
      const voteType = doc.data().voteType;
      stats.total++;
      if (stats[voteType] !== undefined) {
        stats[voteType]++;
      }
    });

    // Get user's vote if userId provided
    let userVote = null;
    if (userId) {
      const userVoteQuery = await db.collection(collections.VOTES)
        .where('linkId', '==', linkId)
        .where('userId', '==', userId)
        .get();

      if (!userVoteQuery.empty) {
        const voteDoc = userVoteQuery.docs[0];
        const voteData = voteDoc.data();

        userVote = {
          id: voteDoc.id,
          linkId,
          userId: voteData.userId,
          voteType: voteData.voteType,
          createdAt: voteData.createdAt.toISOString()
        };
      }
    }

    res.json({
      success: true,
      data: {
        linkId,
        statistics: stats, // Changed from 'stats' to 'statistics' to match frontend
        userVote
      }
    });
  } catch (error) {
    logger.error('Optimized vote data error', { error: error.message, linkId: req.params.linkId });
    res.status(500).json({
      success: false,
      error: 'Failed to get vote data'
    });
  }
});

// Delete user's vote
router.delete('/:linkId', async (req, res) => {
  try {
    const { linkId } = req.params;
    const userId = req.body.userId || req.query.userId || req.headers['x-user-id'];

    if (!userId) {
      return res.status(400).json({
        success: false,
        error: 'User ID required'
      });
    }

    const userVoteQuery = await db.collection(collections.VOTES)
      .where('linkId', '==', linkId)
      .where('userId', '==', userId)
      .get();

    if (!userVoteQuery.empty) {
      await userVoteQuery.docs[0].ref.delete();

      // Update vote count on the post
      await updatePostVoteCount(linkId);

      logger.info('Vote deleted', { linkId, userId });
    }

    res.json({
      success: true,
      message: 'Vote removed successfully',
      linkId
    });
  } catch (error) {
    logger.error('Delete vote error', { error: error.message, linkId: req.params.linkId });
    res.status(500).json({
      success: false,
      error: 'Failed to delete vote'
    });
  }
});

// Legacy route for backward compatibility
router.post('/', (req, res) => {
  res.json({
    success: true,
    message: 'Vote recorded successfully',
    vote: {
      id: '1',
      postId: req.body.postId,
      userId: '<EMAIL>',
      type: req.body.type, // 'up' or 'down'
      createdAt: new Date().toISOString()
    }
  });
});

// Legacy delete route
router.delete('/:id', (req, res) => {
  res.json({
    success: true,
    message: 'Vote removed successfully'
  });
});

module.exports = router;
