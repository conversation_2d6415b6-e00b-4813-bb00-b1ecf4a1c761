version: '3.8'

# Optimized Docker Compose with performance improvements
services:
  # Redis for caching with optimized configuration
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    command: >
      redis-server 
      --appendonly yes 
      --maxmemory 512mb 
      --maxmemory-policy allkeys-lru
      --tcp-keepalive 60
      --timeout 300
    volumes:
      - redis-data:/data
    networks:
      - app-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3

  # Authentication Service with optimizations
  auth-service:
    build:
      context: .
      dockerfile: shared/Dockerfile.optimized
      target: production
      args:
        SERVICE_NAME: auth-service
        SERVICE_PORT: 3001
      cache_from:
        - auth-service:cache
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - PORT=3001
      - NODE_OPTIONS=--max-old-space-size=512
    env_file:
      - .env
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - app-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Link Verification Service
  link-service:
    build:
      context: .
      dockerfile: shared/Dockerfile.optimized
      target: production
      args:
        SERVICE_NAME: link-service
        SERVICE_PORT: 3002
      cache_from:
        - link-service:cache
    ports:
      - "3002:3002"
    environment:
      - NODE_ENV=production
      - PORT=3002
      - NODE_OPTIONS=--max-old-space-size=512
    env_file:
      - .env
    depends_on:
      auth-service:
        condition: service_healthy
    networks:
      - app-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3002/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # News Service
  news-service:
    build:
      context: .
      dockerfile: shared/Dockerfile.optimized
      target: production
      args:
        SERVICE_NAME: news-service
        SERVICE_PORT: 3003
      cache_from:
        - news-service:cache
    ports:
      - "3003:3003"
    environment:
      - NODE_ENV=production
      - PORT=3003
      - NODE_OPTIONS=--max-old-space-size=512
    env_file:
      - .env
    depends_on:
      auth-service:
        condition: service_healthy
    networks:
      - app-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3003/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Chat Service
  chat-service:
    build:
      context: .
      dockerfile: shared/Dockerfile.optimized
      target: production
      args:
        SERVICE_NAME: chat-service
        SERVICE_PORT: 3004
      cache_from:
        - chat-service:cache
    ports:
      - "3004:3004"
    environment:
      - NODE_ENV=production
      - PORT=3004
      - NODE_OPTIONS=--max-old-space-size=512
    env_file:
      - .env
    depends_on:
      auth-service:
        condition: service_healthy
    networks:
      - app-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3004/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Community Service
  community-service:
    build:
      context: .
      dockerfile: shared/Dockerfile.optimized
      target: production
      args:
        SERVICE_NAME: community-service
        SERVICE_PORT: 3005
      cache_from:
        - community-service:cache
    ports:
      - "3005:3005"
    environment:
      - NODE_ENV=production
      - PORT=3005
      - NODE_OPTIONS=--max-old-space-size=512
    env_file:
      - .env
    depends_on:
      auth-service:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - app-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3005/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Admin Service
  admin-service:
    build:
      context: .
      dockerfile: shared/Dockerfile.optimized
      target: production
      args:
        SERVICE_NAME: admin-service
        SERVICE_PORT: 3006
      cache_from:
        - admin-service:cache
    ports:
      - "3006:3006"
    environment:
      - NODE_ENV=production
      - PORT=3006
      - NODE_OPTIONS=--max-old-space-size=512
    env_file:
      - .env
    depends_on:
      auth-service:
        condition: service_healthy
      community-service:
        condition: service_healthy
      link-service:
        condition: service_healthy
    networks:
      - app-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3006/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # API Gateway with load balancing
  api-gateway:
    build:
      context: .
      dockerfile: services/api-gateway/Dockerfile
      target: production
      cache_from:
        - api-gateway:cache
    ports:
      - "8080:8080"
    environment:
      - NODE_ENV=production
      - PORT=8080
      - NODE_OPTIONS=--max-old-space-size=1024
    env_file:
      - .env
    depends_on:
      redis:
        condition: service_healthy
      auth-service:
        condition: service_healthy
      link-service:
        condition: service_healthy
      community-service:
        condition: service_healthy
      chat-service:
        condition: service_healthy
      news-service:
        condition: service_healthy
      admin-service:
        condition: service_healthy
    networks:
      - app-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # React Frontend with optimized nginx
  frontend:
    build:
      context: client
      dockerfile: Dockerfile.optimized
      target: production
      cache_from:
        - frontend:cache
    ports:
      - "3000:3000"
    environment:
      - NGINX_WORKER_PROCESSES=auto
      - NGINX_WORKER_CONNECTIONS=1024
    env_file:
      - .env
    depends_on:
      api-gateway:
        condition: service_healthy
    networks:
      - app-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.5'
        reservations:
          memory: 128M
          cpus: '0.25'
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

volumes:
  redis-data:
    driver: local

networks:
  app-network:
    driver: bridge
    driver_opts:
      com.docker.network.bridge.name: anti-fraud-br0
    ipam:
      config:
        - subnet: **********/16
