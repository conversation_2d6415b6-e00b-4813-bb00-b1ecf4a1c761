{"name": "factcheck-platform", "version": "1.0.0", "description": "FactCheck Anti-Fraud Platform - Microservices Architecture", "private": true, "scripts": {"start": "echo Starting Full Stack Application... && npm run start:full", "dev": "echo Starting development environment... && npm run dev:full", "stop": "npm run kill:all", "restart": "npm run stop && npm run start", "clean": "npm run stop && npm run docker:down && docker system prune -f", "setup": "npm run install:all && npm run setup:env", "start:full": "concurrently --kill-others-on-fail \"npm run start:services\" \"npm run start:client:delayed\"", "start:services": "concurrently --kill-others-on-fail \"npm run start:auth\" \"npm run start:api-gateway\" \"npm run start:admin\" \"npm run start:chat\" \"npm run start:community\" \"npm run start:link\" \"npm run start:news\"", "start:client": "cd client && npm start", "start:client:delayed": "node scripts/utils/delay.js 20 && npm run start:client", "start:safe": "node scripts/utils/start-all-fixed.js", "dev:full": "concurrently --kill-others-on-fail \"npm run dev:services\" \"npm run dev:client:delayed\"", "dev:services": "concurrently --kill-others-on-fail \"npm run dev:auth\" \"npm run dev:api-gateway\" \"npm run dev:admin\" \"npm run dev:chat\" \"npm run dev:community\" \"npm run dev:link\" \"npm run dev:news\"", "dev:client": "cd client && npm run start", "dev:client:delayed": "node scripts/utils/delay.js 20 && npm run dev:client", "start:auth": "cd services/auth-service && npm start", "start:api-gateway": "cd services/api-gateway && npm start", "start:admin": "cd services/admin-service && npm start", "start:chat": "cd services/chat-service && npm start", "start:community": "cd services/community-service && npm start", "start:link": "cd services/link-service && npm start", "start:news": "cd services/news-service && npm start", "dev:auth": "cd services/auth-service && npm run dev", "dev:api-gateway": "cd services/api-gateway && npm run dev", "dev:admin": "cd services/admin-service && npm run dev", "dev:chat": "cd services/chat-service && npm run dev", "dev:community": "cd services/community-service && npm run dev", "dev:link": "cd services/link-service && npm run dev", "dev:news": "cd services/news-service && npm run dev", "docker:check": "scripts\\check-docker.bat", "docker:fix": "scripts\\fix-docker.bat", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:build": "docker-compose build", "docker:build:optimized": "bash scripts/build-optimized.sh", "docker:build:parallel": "bash scripts/build-optimized.sh --parallel 6", "docker:logs": "docker-compose logs -f", "docker:restart": "npm run docker:down && npm run docker:up", "docker:status": "scripts\\docker-status-vi.bat", "docker:optimize": "bash scripts/optimize-dockerfiles.sh", "docker:optimize:all": "bash scripts/optimize-all.sh", "docker:clean:cache": "bash scripts/build-optimized.sh --clean-cache", "docker:monitor": "bash scripts/monitor-build-performance.sh", "db:migrate": "node scripts/migrate-firestore.js", "db:migrate:dry-run": "node scripts/migrate-firestore.js --dry-run", "db:seed": "node scripts/seed-database.js", "db:migrate:production": "node scripts/migrate-firestore.js --production", "db:analyze": "node scripts/analyze-production-db.js", "deploy": "scripts\\quick-deploy.bat", "deploy:local": "scripts\\deploy-local.bat", "deploy:docker": "scripts\\deploy-docker-windows.bat", "deploy:k8s": "scripts\\deploy-k8s.sh", "k8s:apply": "kubectl apply -f k8s/", "k8s:delete": "kubectl delete -f k8s/", "k8s:status": "kubectl get pods -n anti-fraud-platform", "k8s:logs": "kubectl logs -f deployment/api-gateway -n anti-fraud-platform", "monitoring:install": "node scripts/install-monitoring.js", "monitoring:start": "node scripts/start-monitoring.js", "monitoring:stop": "node scripts/stop-monitoring.js", "monitoring:status": "node scripts/check-monitoring-status.js", "health": "curl -s http://localhost:8080/services/status || echo Services not running", "health:frontend": "curl -s http://localhost:3000 || echo Frontend not running", "health:api": "curl -s http://localhost:8080/health || echo API not running", "status": "npm run health && npm run health:frontend", "logs": "npm run logs:docker", "logs:docker": "docker-compose logs -f", "logs:services": "echo Use: npm run logs:service-name (auth, api-gateway, etc.)", "install:all": "echo Installing all dependencies... && npm install && cd client && npm install && cd ../services/auth-service && npm install && cd ../api-gateway && npm install && cd ../admin-service && npm install && cd ../chat-service && npm install && cd ../community-service && npm install && cd ../link-service && npm install && cd ../news-service && npm install && cd ../../ && echo All dependencies installed!", "fix:ports": "node scripts/utils/fix-port-conflicts.js", "validate:ports": "node scripts/utils/validate-ports.js", "kill:all": "node scripts/utils/kill-all-services.js", "setup:env": "echo Please configure .env file with your Firebase credentials", "info": "echo Frontend: http://localhost:3000, API: http://localhost:8080", "open": "start http://localhost:3000", "open:api": "start http://localhost:8080", "help": "scripts\\help-fixed.bat"}, "dependencies": {"concurrently": "^8.2.2", "prom-client": "^15.1.0"}, "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "workspaces": ["client", "services/*"]}